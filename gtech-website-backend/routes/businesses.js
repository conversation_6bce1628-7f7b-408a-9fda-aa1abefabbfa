const express = require('express');
const { body, validationResult } = require('express-validator');
const Business = require('../models/Business');
const User = require('../models/User');
const { protect, restrictTo, optionalAuth, checkOwnership } = require('../middleware/auth');

const router = express.Router();

// Validation middleware for business creation
const validateBusiness = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Business name must be between 2 and 100 characters'),
  body('description')
    .trim()
    .isLength({ min: 10, max: 1000 })
    .withMessage('Description must be between 10 and 1000 characters'),
  body('category')
    .isIn(['Restaurant', 'Retail', 'Healthcare', 'Education', 'Technology', 'Finance', 'Real Estate', 'Automotive', 'Beauty & Wellness', 'Entertainment', 'Professional Services', 'Home Services', 'Travel & Tourism', 'Sports & Recreation', 'Non-Profit', 'Other'])
    .withMessage('Invalid business category'),
  body('contact.email')
    .isEmail()
    .withMessage('Please provide a valid business email'),
  body('contact.phone')
    .isMobilePhone()
    .withMessage('Please provide a valid phone number'),
  body('location.address')
    .trim()
    .notEmpty()
    .withMessage('Business address is required'),
  body('location.city')
    .trim()
    .notEmpty()
    .withMessage('City is required'),
  body('location.state')
    .trim()
    .notEmpty()
    .withMessage('State is required')
];

// Get all businesses with filtering and search
router.get('/', optionalAuth, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 12;
    const skip = (page - 1) * limit;

    // Build filter
    const filter = { status: 'active' };
    
    if (req.query.category) filter.category = req.query.category;
    if (req.query.city) filter['location.city'] = new RegExp(req.query.city, 'i');
    if (req.query.state) filter['location.state'] = new RegExp(req.query.state, 'i');
    if (req.query.featured === 'true') filter.featured = true;
    if (req.query.verified === 'true') filter.isVerified = true;

    // Search functionality
    if (req.query.search) {
      filter.$text = { $search: req.query.search };
    }

    // Build sort
    let sort = {};
    switch (req.query.sort) {
      case 'rating':
        sort = { 'rating.average': -1, 'rating.count': -1 };
        break;
      case 'newest':
        sort = { createdAt: -1 };
        break;
      case 'oldest':
        sort = { createdAt: 1 };
        break;
      case 'name':
        sort = { name: 1 };
        break;
      default:
        sort = { featured: -1, 'rating.average': -1, createdAt: -1 };
    }

    const businesses = await Business.find(filter)
      .populate('owner', 'firstName lastName avatar')
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .select('-employees -verificationDocuments -aiInsights');

    const total = await Business.countDocuments(filter);

    res.status(200).json({
      status: 'success',
      results: businesses.length,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      data: {
        businesses
      }
    });
  } catch (error) {
    console.error('Get businesses error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Error fetching businesses'
    });
  }
});

// Get business by ID
router.get('/:id', optionalAuth, async (req, res) => {
  try {
    const business = await Business.findById(req.params.id)
      .populate('owner', 'firstName lastName avatar bio')
      .populate('employees.user', 'firstName lastName avatar')
      .populate({
        path: 'reviews',
        match: { status: 'approved' },
        populate: {
          path: 'user',
          select: 'firstName lastName avatar'
        },
        options: { sort: { createdAt: -1 }, limit: 10 }
      });

    if (!business) {
      return res.status(404).json({
        status: 'error',
        message: 'Business not found'
      });
    }

    // Increment view count
    business.views += 1;
    await business.save({ validateBeforeSave: false });

    res.status(200).json({
      status: 'success',
      data: {
        business
      }
    });
  } catch (error) {
    console.error('Get business error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Error fetching business'
    });
  }
});

// Create new business
router.post('/', protect, validateBusiness, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        status: 'error',
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    // Check if user already has a business
    const existingBusiness = await Business.findOne({ owner: req.user._id });
    if (existingBusiness) {
      return res.status(400).json({
        status: 'error',
        message: 'You already have a business registered. Please contact support to register additional businesses.'
      });
    }

    // Create business
    const businessData = {
      ...req.body,
      owner: req.user._id
    };

    const business = await Business.create(businessData);

    // Update user role to business_owner
    await User.findByIdAndUpdate(req.user._id, {
      role: 'business_owner',
      businessProfile: business._id
    });

    const populatedBusiness = await Business.findById(business._id)
      .populate('owner', 'firstName lastName avatar');

    res.status(201).json({
      status: 'success',
      message: 'Business created successfully',
      data: {
        business: populatedBusiness
      }
    });
  } catch (error) {
    console.error('Create business error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Error creating business'
    });
  }
});

// Update business
router.patch('/:id', protect, checkOwnership(Business), async (req, res) => {
  try {
    const allowedFields = [
      'name', 'description', 'category', 'subcategory', 'contact', 
      'location', 'businessHours', 'images', 'logo', 'tags'
    ];
    
    const filteredBody = {};
    Object.keys(req.body).forEach(key => {
      if (allowedFields.includes(key)) {
        filteredBody[key] = req.body[key];
      }
    });

    const business = await Business.findByIdAndUpdate(
      req.params.id,
      filteredBody,
      {
        new: true,
        runValidators: true
      }
    ).populate('owner', 'firstName lastName avatar');

    res.status(200).json({
      status: 'success',
      message: 'Business updated successfully',
      data: {
        business
      }
    });
  } catch (error) {
    console.error('Update business error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Error updating business'
    });
  }
});

// Delete business
router.delete('/:id', protect, checkOwnership(Business), async (req, res) => {
  try {
    await Business.findByIdAndUpdate(req.params.id, { status: 'inactive' });

    res.status(200).json({
      status: 'success',
      message: 'Business deactivated successfully'
    });
  } catch (error) {
    console.error('Delete business error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Error deleting business'
    });
  }
});

module.exports = router;
