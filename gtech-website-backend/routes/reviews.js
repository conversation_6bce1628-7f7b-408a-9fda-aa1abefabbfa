const express = require('express');
const { body, validationResult } = require('express-validator');
const Review = require('../models/Review');
const Business = require('../models/Business');
const { protect, restrictTo, optionalAuth, checkOwnership } = require('../middleware/auth');

const router = express.Router();

// Validation middleware for review creation
const validateReview = [
  body('business')
    .isMongoId()
    .withMessage('Invalid business ID'),
  body('rating')
    .isInt({ min: 1, max: 5 })
    .withMessage('Rating must be between 1 and 5'),
  body('title')
    .trim()
    .isLength({ min: 5, max: 100 })
    .withMessage('Review title must be between 5 and 100 characters'),
  body('comment')
    .trim()
    .isLength({ min: 10, max: 1000 })
    .withMessage('Review comment must be between 10 and 1000 characters'),
  body('categories.service')
    .optional()
    .isInt({ min: 1, max: 5 })
    .withMessage('Service rating must be between 1 and 5'),
  body('categories.quality')
    .optional()
    .isInt({ min: 1, max: 5 })
    .withMessage('Quality rating must be between 1 and 5'),
  body('categories.value')
    .optional()
    .isInt({ min: 1, max: 5 })
    .withMessage('Value rating must be between 1 and 5'),
  body('categories.atmosphere')
    .optional()
    .isInt({ min: 1, max: 5 })
    .withMessage('Atmosphere rating must be between 1 and 5')
];

// Get all reviews with filtering
router.get('/', optionalAuth, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Build filter
    const filter = { status: 'approved' };
    
    if (req.query.business) filter.business = req.query.business;
    if (req.query.user) filter.user = req.query.user;
    if (req.query.rating) filter.rating = parseInt(req.query.rating);
    if (req.query.minRating) filter.rating = { $gte: parseInt(req.query.minRating) };

    // Build sort
    let sort = {};
    switch (req.query.sort) {
      case 'rating_high':
        sort = { rating: -1, createdAt: -1 };
        break;
      case 'rating_low':
        sort = { rating: 1, createdAt: -1 };
        break;
      case 'oldest':
        sort = { createdAt: 1 };
        break;
      case 'helpful':
        sort = { 'helpful.count': -1, createdAt: -1 };
        break;
      default:
        sort = { createdAt: -1 };
    }

    const reviews = await Review.find(filter)
      .populate('user', 'firstName lastName avatar')
      .populate('business', 'name category location.city')
      .populate('response.respondedBy', 'firstName lastName')
      .sort(sort)
      .skip(skip)
      .limit(limit);

    const total = await Review.countDocuments(filter);

    res.status(200).json({
      status: 'success',
      results: reviews.length,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      data: {
        reviews
      }
    });
  } catch (error) {
    console.error('Get reviews error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Error fetching reviews'
    });
  }
});

// Get review by ID
router.get('/:id', optionalAuth, async (req, res) => {
  try {
    const review = await Review.findById(req.params.id)
      .populate('user', 'firstName lastName avatar bio')
      .populate('business', 'name category location rating')
      .populate('response.respondedBy', 'firstName lastName avatar');

    if (!review) {
      return res.status(404).json({
        status: 'error',
        message: 'Review not found'
      });
    }

    res.status(200).json({
      status: 'success',
      data: {
        review
      }
    });
  } catch (error) {
    console.error('Get review error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Error fetching review'
    });
  }
});

// Create new review
router.post('/', protect, validateReview, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        status: 'error',
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { business: businessId } = req.body;

    // Check if business exists
    const business = await Business.findById(businessId);
    if (!business) {
      return res.status(404).json({
        status: 'error',
        message: 'Business not found'
      });
    }

    // Check if user already reviewed this business
    const existingReview = await Review.findOne({
      business: businessId,
      user: req.user._id
    });

    if (existingReview) {
      return res.status(400).json({
        status: 'error',
        message: 'You have already reviewed this business'
      });
    }

    // Create review
    const reviewData = {
      ...req.body,
      user: req.user._id
    };

    const review = await Review.create(reviewData);

    const populatedReview = await Review.findById(review._id)
      .populate('user', 'firstName lastName avatar')
      .populate('business', 'name category');

    res.status(201).json({
      status: 'success',
      message: 'Review created successfully',
      data: {
        review: populatedReview
      }
    });
  } catch (error) {
    console.error('Create review error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Error creating review'
    });
  }
});

// Update review
router.patch('/:id', protect, checkOwnership(Review), async (req, res) => {
  try {
    const allowedFields = ['rating', 'title', 'comment', 'categories', 'images'];
    const filteredBody = {};
    
    Object.keys(req.body).forEach(key => {
      if (allowedFields.includes(key)) {
        filteredBody[key] = req.body[key];
      }
    });

    // Store original values for edit history
    req.resource._original = {
      rating: req.resource.rating,
      comment: req.resource.comment
    };

    const review = await Review.findByIdAndUpdate(
      req.params.id,
      filteredBody,
      {
        new: true,
        runValidators: true
      }
    ).populate('user', 'firstName lastName avatar')
     .populate('business', 'name category');

    res.status(200).json({
      status: 'success',
      message: 'Review updated successfully',
      data: {
        review
      }
    });
  } catch (error) {
    console.error('Update review error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Error updating review'
    });
  }
});

// Delete review
router.delete('/:id', protect, checkOwnership(Review), async (req, res) => {
  try {
    await Review.findByIdAndDelete(req.params.id);

    res.status(200).json({
      status: 'success',
      message: 'Review deleted successfully'
    });
  } catch (error) {
    console.error('Delete review error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Error deleting review'
    });
  }
});

// Mark review as helpful
router.post('/:id/helpful', protect, async (req, res) => {
  try {
    const review = await Review.findById(req.params.id);
    
    if (!review) {
      return res.status(404).json({
        status: 'error',
        message: 'Review not found'
      });
    }

    // Check if user already marked as helpful
    const alreadyMarked = review.helpful.users.includes(req.user._id);
    
    if (alreadyMarked) {
      // Remove helpful mark
      review.helpful.users.pull(req.user._id);
      review.helpful.count = Math.max(0, review.helpful.count - 1);
    } else {
      // Add helpful mark
      review.helpful.users.push(req.user._id);
      review.helpful.count += 1;
    }

    await review.save();

    res.status(200).json({
      status: 'success',
      message: alreadyMarked ? 'Helpful mark removed' : 'Review marked as helpful',
      data: {
        helpful: {
          count: review.helpful.count,
          marked: !alreadyMarked
        }
      }
    });
  } catch (error) {
    console.error('Mark helpful error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Error updating helpful status'
    });
  }
});

module.exports = router;
