# Nexa Business Platform Backend

A comprehensive Node.js backend API for Nexa - Nigeria's #1 AI Local Business Platform. This backend provides authentication, business management, reviews, and AI-powered features for local businesses.

## Features

### Authentication
- **Regular Authentication**: Email/password signup and login
- **Google OAuth**: Seamless Google account integration
- **JWT Tokens**: Secure token-based authentication
- **Password Security**: Bcrypt hashing with salt rounds
- **Role-based Access**: User, Business Owner, and Admin roles

### Business Management
- **Business Profiles**: Complete business information management
- **Categories**: 16+ business categories with subcategories
- **Location Services**: Address, coordinates, and location-based search
- **Business Hours**: Flexible scheduling system
- **Media Management**: Logo and image uploads
- **Verification System**: Business verification with document uploads

### Review System
- **User Reviews**: 5-star rating system with detailed feedback
- **Category Ratings**: Service, Quality, Value, and Atmosphere ratings
- **Review Moderation**: Admin approval system
- **Business Responses**: Business owners can respond to reviews
- **Helpful Votes**: Community-driven review quality assessment

### AI Features
- **Business Insights**: AI-powered performance analysis
- **Review Analysis**: Sentiment analysis and keyword extraction
- **Recommendations**: AI-generated business improvement suggestions
- **Search Enhancement**: AI-powered search and discovery

## Tech Stack

- **Runtime**: Node.js
- **Framework**: Express.js
- **Database**: MongoDB with Mongoose ODM
- **Authentication**: Passport.js (Local & Google OAuth)
- **Security**: Helmet, CORS, Rate Limiting
- **Validation**: Express Validator
- **File Upload**: Multer
- **Email**: Nodemailer

## Installation

1. **Clone the repository**
   ```bash
   <NAME_EMAIL>:Gtech-Team/gtech-website-backend.git
   cd gtech-website-backend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.example .env
   ```

   Edit `.env` with your configuration:
   ```env
   PORT=5000
   NODE_ENV=development
   MONGODB_URI=mongodb://localhost:27017/nexa-business-platform
   JWT_SECRET=your-super-secret-jwt-key
   GOOGLE_CLIENT_ID=your-google-client-id
   GOOGLE_CLIENT_SECRET=your-google-client-secret
   FRONTEND_URL=https://nexa-frontend-sage.vercel.app
   ```

4. **Start MongoDB**
   Make sure MongoDB is running on your system.

5. **Run the application**
   ```bash
   # Development mode with auto-reload
   npm run dev

   # Production mode
   npm start
   ```

## API Documentation

### Base URL
```
http://localhost:5000/api
```

### Authentication Endpoints

#### Register User
```http
POST /api/auth/signup
Content-Type: application/json

{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "password": "SecurePass123",
  "phone": "+2348012345678"
}
```

#### Login User
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "SecurePass123"
}
```

#### Google OAuth
```http
GET /api/auth/google
```

#### Get Current User
```http
GET /api/auth/me
Authorization: Bearer <jwt_token>
```

### Business Endpoints

#### Get All Businesses
```http
GET /api/businesses?page=1&limit=12&category=Restaurant&city=Lagos&search=pizza
```

#### Get Business by ID
```http
GET /api/businesses/:id
```

#### Create Business
```http
POST /api/businesses
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "name": "Amazing Restaurant",
  "description": "Best local cuisine in Lagos",
  "category": "Restaurant",
  "contact": {
    "email": "<EMAIL>",
    "phone": "+2348012345678",
    "website": "https://amazing.com"
  },
  "location": {
    "address": "123 Main Street",
    "city": "Lagos",
    "state": "Lagos"
  }
}
```

### Review Endpoints

#### Get Reviews
```http
GET /api/reviews?business=:businessId&sort=newest
```

#### Create Review
```http
POST /api/reviews
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "business": "business_id_here",
  "rating": 5,
  "title": "Excellent service!",
  "comment": "Had a wonderful experience...",
  "categories": {
    "service": 5,
    "quality": 4,
    "value": 5,
    "atmosphere": 4
  }
}
```

## Database Schema

### User Model
- Personal information (name, email, phone)
- Authentication (password, Google ID)
- Profile (avatar, bio, location)
- Preferences and settings
- Role-based permissions

### Business Model
- Business details (name, description, category)
- Contact information
- Location and coordinates
- Business hours
- Media (logo, images)
- Ratings and reviews
- Verification status

### Review Model
- Rating (1-5 stars)
- Detailed feedback
- Category-specific ratings
- Media attachments
- Moderation status
- Business responses

## Security Features

- **Rate Limiting**: Prevents API abuse
- **CORS Protection**: Configurable cross-origin requests
- **Helmet Security**: Security headers and protection
- **Input Validation**: Comprehensive request validation
- **Password Hashing**: Bcrypt with salt rounds
- **JWT Authentication**: Secure token-based auth
- **Role-based Access**: Granular permission system

## Development

### Project Structure
```
├── config/
│   └── passport.js          # Passport configuration
├── middleware/
│   ├── auth.js              # Authentication middleware
│   └── errorHandler.js      # Error handling
├── models/
│   ├── User.js              # User schema
│   ├── Business.js          # Business schema
│   └── Review.js            # Review schema
├── routes/
│   ├── auth.js              # Authentication routes
│   ├── users.js             # User management
│   ├── businesses.js        # Business operations
│   └── reviews.js           # Review system
├── server.js                # Main application file
└── package.json
```

### Available Scripts
- `npm start` - Start production server
- `npm run dev` - Start development server with auto-reload

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the ISC License.