#!/usr/bin/env node
const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

async function testAPI() {
  console.log('🚀 Testing Nexa Backend API...\n');

  try {
    // Test 1: Health Check
    console.log('1. Testing Health Check...');
    const healthResponse = await axios.get(`${BASE_URL}/health`);
    console.log('✅ Health Check:', healthResponse.data.message);
    console.log('');

    // Test 2: User Registration
    console.log('2. Testing User Registration...');
    const userData = {
      firstName: 'John',
      lastName: 'Doe',
      email: `test${Date.now()}@example.com`,
      password: 'SecurePass123',
      phone: '+2348012345678'
    };

    try {
      const registerResponse = await axios.post(`${BASE_URL}/auth/signup`, userData);
      console.log('✅ User Registration successful');
      console.log('User ID:', registerResponse.data.data.user._id);
      console.log('Token received:', !!registerResponse.data.token);
      
      const token = registerResponse.data.token;
      
      // Test 3: Get Current User
      console.log('\n3. Testing Get Current User...');
      const userResponse = await axios.get(`${BASE_URL}/auth/me`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      console.log('✅ Current User:', userResponse.data.data.user.fullName);
      
      // Test 4: Get Businesses (should work even without data)
      console.log('\n4. Testing Get Businesses...');
      const businessesResponse = await axios.get(`${BASE_URL}/businesses`);
      console.log('✅ Businesses endpoint accessible');
      console.log('Results:', businessesResponse.data.results || 0);
      
      // Test 5: Create Business (requires authentication)
      console.log('\n5. Testing Create Business...');
      const businessData = {
        name: 'Test Restaurant',
        description: 'A great place to eat delicious local cuisine',
        category: 'Restaurant',
        contact: {
          email: '<EMAIL>',
          phone: '+2348012345679'
        },
        location: {
          address: '123 Test Street',
          city: 'Lagos',
          state: 'Lagos'
        }
      };
      
      const businessResponse = await axios.post(`${BASE_URL}/businesses`, businessData, {
        headers: { Authorization: `Bearer ${token}` }
      });
      console.log('✅ Business created successfully');
      console.log('Business ID:', businessResponse.data.data.business._id);
      
      console.log('\n🎉 All tests passed! The Nexa Backend API is working correctly.');
      
    } catch (authError) {
      if (authError.response?.status === 400 && authError.response?.data?.message?.includes('already exists')) {
        console.log('⚠️  User already exists (this is expected in repeated tests)');
      } else {
        throw authError;
      }
    }

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data?.message || error.message);
    console.error('Status:', error.response?.status);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('\n💡 Make sure the server is running with: npm run dev');
    }
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  testAPI();
}

module.exports = testAPI;
