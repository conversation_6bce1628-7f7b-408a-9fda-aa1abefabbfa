** READ THIS FIRST! **

#### Are you looking for help?

Reminder: The issue tracker is not a support forum.

Issues should only be filed in this project once they are able to be reproduced
and confirmed as a flaw in the software or incorrect information in associated
documention.

If you are encountering problems integrating this module into your application,
please post a question on the [discussion forum](https://github.com/passport/discuss)
rather than filing an issue.

#### Is this a security issue?

Do not open issues that might have security implications.  Potential security
vulnerabilities should be reported privately to jar<PERSON><PERSON><PERSON>@gmail.com.  Once any
vulerabilities have been repaired, the details will be disclosed publicly in a
responsible manner.  This also allows time for coordinating with affected parties
in order to mitigate negative consequences.


If neither of the above two scenarios apply to your situation, you should open
an issue.  Delete this paragraph and the text above, and fill in the information
requested below.

<!-- Provide a brief summary of the issue in the title field above. -->

<!-- Provide a detailed description of your use case, including as much -->
<!-- detail as possible about what you are trying to accomplish and why. -->

### Expected behavior
<!-- Provide a detailed description of how you expected the software to -->
<!-- behave. -->

### Actual behavior
<!-- Provide a detailed description of how the software actually behaved, -->
<!-- including any rationale for why that behavior is incorrect. -->

### Steps to reproduce
<!-- Provide an unambiguous series of steps that can be used to reproduce -->
<!-- this issue, including any code if applicable. -->

```js
// Format code using Markdown code blocks
```

### Environment

* Operating System: 
* Node version: <!-- $ node -v -->
* passport version: <!-- $ npm list passport -->
* passport-google-oauth2 version: <!-- $ npm list passport-google-oauth2 -->
