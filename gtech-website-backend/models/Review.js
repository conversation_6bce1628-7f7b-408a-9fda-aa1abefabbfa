const mongoose = require('mongoose');

const reviewSchema = new mongoose.Schema({
  business: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Business',
    required: [true, 'Review must belong to a business']
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Review must belong to a user']
  },
  rating: {
    type: Number,
    required: [true, 'Rating is required'],
    min: [1, 'Rating must be at least 1'],
    max: [5, 'Rating cannot exceed 5']
  },
  title: {
    type: String,
    required: [true, 'Review title is required'],
    trim: true,
    maxlength: [100, 'Review title cannot exceed 100 characters']
  },
  comment: {
    type: String,
    required: [true, 'Review comment is required'],
    maxlength: [1000, 'Review comment cannot exceed 1000 characters']
  },
  
  // Review categories for detailed feedback
  categories: {
    service: {
      type: Number,
      min: 1,
      max: 5
    },
    quality: {
      type: Number,
      min: 1,
      max: 5
    },
    value: {
      type: Number,
      min: 1,
      max: 5
    },
    atmosphere: {
      type: Number,
      min: 1,
      max: 5
    }
  },
  
  // Media attachments
  images: [{
    url: String,
    caption: String
  }],
  
  // Review status and moderation
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected', 'flagged'],
    default: 'pending'
  },
  moderationNotes: String,
  
  // Interaction tracking
  helpful: {
    count: {
      type: Number,
      default: 0
    },
    users: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }]
  },
  
  // Business response
  response: {
    text: String,
    respondedAt: Date,
    respondedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  },
  
  // AI Analysis
  aiAnalysis: {
    sentiment: {
      type: String,
      enum: ['positive', 'neutral', 'negative']
    },
    keywords: [String],
    topics: [String],
    authenticity: {
      score: {
        type: Number,
        min: 0,
        max: 1
      },
      flags: [String]
    }
  },
  
  // Visit verification
  visitVerified: {
    type: Boolean,
    default: false
  },
  visitDate: Date,
  
  // Reporting and flags
  reports: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    reason: {
      type: String,
      enum: ['spam', 'inappropriate', 'fake', 'offensive', 'other']
    },
    description: String,
    reportedAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  // Update tracking
  isEdited: {
    type: Boolean,
    default: false
  },
  editHistory: [{
    editedAt: Date,
    previousRating: Number,
    previousComment: String
  }]
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Compound index to prevent duplicate reviews from same user for same business
reviewSchema.index({ business: 1, user: 1 }, { unique: true });

// Other indexes for performance
reviewSchema.index({ business: 1, status: 1, createdAt: -1 });
reviewSchema.index({ user: 1, createdAt: -1 });
reviewSchema.index({ rating: -1 });
reviewSchema.index({ status: 1 });

// Virtual for overall rating calculation
reviewSchema.virtual('overallRating').get(function() {
  if (this.categories) {
    const categories = this.categories;
    const validRatings = Object.values(categories).filter(rating => rating && rating > 0);
    if (validRatings.length > 0) {
      return validRatings.reduce((sum, rating) => sum + rating, 0) / validRatings.length;
    }
  }
  return this.rating;
});

// Static method to calculate average rating for a business
reviewSchema.statics.calcAverageRatings = async function(businessId) {
  const stats = await this.aggregate([
    {
      $match: { business: businessId, status: 'approved' }
    },
    {
      $group: {
        _id: '$business',
        nRating: { $sum: 1 },
        avgRating: { $avg: '$rating' }
      }
    }
  ]);

  if (stats.length > 0) {
    await this.model('Business').findByIdAndUpdate(businessId, {
      'rating.average': Math.round(stats[0].avgRating * 10) / 10,
      'rating.count': stats[0].nRating
    });
  } else {
    await this.model('Business').findByIdAndUpdate(businessId, {
      'rating.average': 0,
      'rating.count': 0
    });
  }
};

// Post-save middleware to update business rating
reviewSchema.post('save', function() {
  this.constructor.calcAverageRatings(this.business);
});

// Post-remove middleware to update business rating
reviewSchema.post('remove', function() {
  this.constructor.calcAverageRatings(this.business);
});

// Pre-save middleware for edit tracking
reviewSchema.pre('save', function(next) {
  if (this.isModified('rating') || this.isModified('comment')) {
    if (!this.isNew) {
      this.isEdited = true;
      this.editHistory.push({
        editedAt: new Date(),
        previousRating: this._original?.rating,
        previousComment: this._original?.comment
      });
    }
  }
  next();
});

module.exports = mongoose.model('Review', reviewSchema);
