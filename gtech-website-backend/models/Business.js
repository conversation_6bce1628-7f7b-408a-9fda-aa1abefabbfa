const mongoose = require('mongoose');

const businessSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Business name is required'],
    trim: true,
    maxlength: [100, 'Business name cannot exceed 100 characters']
  },
  description: {
    type: String,
    required: [true, 'Business description is required'],
    maxlength: [1000, 'Description cannot exceed 1000 characters']
  },
  category: {
    type: String,
    required: [true, 'Business category is required'],
    enum: [
      'Restaurant', 'Retail', 'Healthcare', 'Education', 'Technology',
      'Finance', 'Real Estate', 'Automotive', 'Beauty & Wellness',
      'Entertainment', 'Professional Services', 'Home Services',
      'Travel & Tourism', 'Sports & Recreation', 'Non-Profit', 'Other'
    ]
  },
  subcategory: {
    type: String,
    trim: true
  },
  
  // Contact Information
  contact: {
    email: {
      type: String,
      required: [true, 'Business email is required'],
      lowercase: true,
      match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
    },
    phone: {
      type: String,
      required: [true, 'Business phone is required'],
      match: [/^[\+]?[1-9][\d]{0,15}$/, 'Please enter a valid phone number']
    },
    website: {
      type: String,
      match: [/^https?:\/\/.+/, 'Please enter a valid website URL']
    },
    socialMedia: {
      facebook: String,
      twitter: String,
      instagram: String,
      linkedin: String
    }
  },
  
  // Location Information
  location: {
    address: {
      type: String,
      required: [true, 'Business address is required']
    },
    city: {
      type: String,
      required: [true, 'City is required']
    },
    state: {
      type: String,
      required: [true, 'State is required']
    },
    country: {
      type: String,
      default: 'Nigeria'
    },
    postalCode: String,
    coordinates: {
      latitude: {
        type: Number,
        min: -90,
        max: 90
      },
      longitude: {
        type: Number,
        min: -180,
        max: 180
      }
    }
  },
  
  // Business Details
  owner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  employees: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    role: {
      type: String,
      enum: ['manager', 'employee', 'admin'],
      default: 'employee'
    },
    joinedAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  // Business Hours
  businessHours: {
    monday: { open: String, close: String, closed: { type: Boolean, default: false } },
    tuesday: { open: String, close: String, closed: { type: Boolean, default: false } },
    wednesday: { open: String, close: String, closed: { type: Boolean, default: false } },
    thursday: { open: String, close: String, closed: { type: Boolean, default: false } },
    friday: { open: String, close: String, closed: { type: Boolean, default: false } },
    saturday: { open: String, close: String, closed: { type: Boolean, default: false } },
    sunday: { open: String, close: String, closed: { type: Boolean, default: true } }
  },
  
  // Media
  images: [{
    url: String,
    caption: String,
    isPrimary: {
      type: Boolean,
      default: false
    }
  }],
  logo: String,
  
  // Ratings and Reviews
  rating: {
    average: {
      type: Number,
      default: 0,
      min: 0,
      max: 5
    },
    count: {
      type: Number,
      default: 0
    }
  },
  
  // Business Status
  isVerified: {
    type: Boolean,
    default: false
  },
  verificationDocuments: [{
    type: String,
    url: String,
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],
  status: {
    type: String,
    enum: ['active', 'inactive', 'pending', 'suspended'],
    default: 'pending'
  },
  
  // AI Features
  aiInsights: {
    lastAnalyzed: Date,
    recommendations: [String],
    performanceScore: {
      type: Number,
      min: 0,
      max: 100
    }
  },
  
  // SEO and Discovery
  tags: [String],
  featured: {
    type: Boolean,
    default: false
  },
  views: {
    type: Number,
    default: 0
  },
  
  // Subscription/Plan
  plan: {
    type: String,
    enum: ['basic', 'premium', 'enterprise'],
    default: 'basic'
  },
  planExpiry: Date
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for reviews
businessSchema.virtual('reviews', {
  ref: 'Review',
  localField: '_id',
  foreignField: 'business'
});

// Indexes for better query performance
businessSchema.index({ name: 'text', description: 'text', tags: 'text' });
businessSchema.index({ category: 1 });
businessSchema.index({ 'location.city': 1, 'location.state': 1 });
businessSchema.index({ 'rating.average': -1 });
businessSchema.index({ featured: -1, 'rating.average': -1 });
businessSchema.index({ owner: 1 });

// Pre-save middleware to ensure only one primary image
businessSchema.pre('save', function(next) {
  if (this.images && this.images.length > 0) {
    let primaryCount = 0;
    this.images.forEach(image => {
      if (image.isPrimary) primaryCount++;
    });
    
    if (primaryCount === 0 && this.images.length > 0) {
      this.images[0].isPrimary = true;
    } else if (primaryCount > 1) {
      let firstPrimaryFound = false;
      this.images.forEach(image => {
        if (image.isPrimary && !firstPrimaryFound) {
          firstPrimaryFound = true;
        } else if (image.isPrimary) {
          image.isPrimary = false;
        }
      });
    }
  }
  next();
});

module.exports = mongoose.model('Business', businessSchema);
