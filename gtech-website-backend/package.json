{"name": "gtech-website-backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/Gtech-Team/gtech-website-backend.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/Gtech-Team/gtech-website-backend/issues"}, "homepage": "https://github.com/Gtech-Team/gtech-website-backend#readme", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.17.0", "morgan": "^1.10.1", "multer": "^2.0.2", "nodemailer": "^7.0.5", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1"}, "devDependencies": {"axios": "^1.11.0", "nodemon": "^3.1.10"}}